import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { Search as SearchIcon, Building2 } from "lucide-react";

const Search = () => {
  const [companyName, setCompanyName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();

  const handleProfiling = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!companyName.trim()) {
      toast({
        title: "Company name required",
        description: "Please enter a company name to profile",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('http://localhost:3001/company_profiling', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ companyName: companyName.trim() }),
      });

      const data = await response.json();

      if (data.success) {
        navigate("/profile", {
          state: {
            companyName: companyName.trim(),
            profileData: data.data
          }
        });
      } else {
        toast({
          title: "Profiling failed",
          description: data.message || "Unable to profile the company. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Profiling error:', error);
      toast({
        title: "Profiling failed",
        description: "Unable to connect to server. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("isAuthenticated");
    localStorage.removeItem("authToken");
    navigate("/login");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-muted/30">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-3">
            <img 
              src="https://assets-global.website-files.com/62e8c47a3461c223c51016f5/62e8c47a3461c267a210173e_Rakamin-logo-blue.svg" 
              alt="Rakamin"
              className="h-8"
            />
            <h1 className="text-2xl font-bold text-foreground">Company Profile Analyzer</h1>
          </div>
          <Button variant="outline" onClick={handleLogout}>
            Logout
          </Button>
        </div>

        <div className="max-w-2xl mx-auto">
          <Card className="card-elegant">
            <CardHeader className="text-center space-y-4">
              <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
                <Building2 className="w-8 h-8 text-primary" />
              </div>
              <div>
                <CardTitle className="text-2xl font-bold">Company Profiling</CardTitle>
                <p className="text-muted-foreground mt-2">
                  Enter a company name to generate a comprehensive profile analysis
                </p>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              <form onSubmit={handleProfiling} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="company" className="text-base font-medium">
                    Company Name
                  </Label>
                  <div className="relative">
                    <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                    <Input
                      id="company"
                      type="text"
                      placeholder="e.g., PT Rakamin Kolektif Madani"
                      value={companyName}
                      onChange={(e) => setCompanyName(e.target.value)}
                      required
                      className="h-14 pl-12 text-lg"
                    />
                  </div>
                </div>
                <Button 
                  type="submit" 
                  className="w-full h-14 text-lg font-medium btn-primary"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Profiling...
                    </div>
                  ) : (
                    "Do Profiling"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Search;