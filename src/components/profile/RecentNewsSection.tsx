import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Newspaper, Calendar } from "lucide-react";

interface RecentNews {
  title: string;
  date: string;
  categories: string[];
  summary: string;
}

interface RecentNewsSectionProps {
  data: RecentNews[];
}

const RecentNewsSection = ({ data }: RecentNewsSectionProps) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'partnership':
        return 'bg-info/10 text-info border-info/20';
      case 'expansion':
        return 'bg-success/10 text-success border-success/20';
      case 'financial results':
        return 'bg-warning/10 text-warning border-warning/20';
      case 'growth':
        return 'bg-primary/10 text-primary border-primary/20';
      case 'supply chain':
        return 'bg-muted text-muted-foreground border-muted';
      case 'csr':
        return 'bg-accent/10 text-accent-foreground border-accent/20';
      default:
        return 'bg-secondary text-secondary-foreground border-secondary';
    }
  };

  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <Newspaper className="w-5 h-5 text-primary" />
          Recent Announcement & News
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {data.map((news, index) => (
          <div key={index} className="space-y-3 pb-4 border-b border-border last:border-b-0 last:pb-0">
            <div>
              <h3 className="font-semibold text-foreground leading-tight mb-2">
                {news.title}
              </h3>
              <div className="flex items-center gap-3 mb-3">
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="w-3 h-3" />
                  {formatDate(news.date)}
                </div>
                <div className="flex gap-2">
                  {news.categories.map((category, catIndex) => (
                    <Badge 
                      key={catIndex} 
                      variant="outline" 
                      className={`text-xs ${getCategoryColor(category)}`}
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {news.summary}
              </p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default RecentNewsSection;