import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp } from "lucide-react";
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer } from 'recharts';

interface GrowthMetrics {
  cagr: {
    percentage: number | null;
    benchmark: string;
    data_points: Array<{
      year: string;
      value: number;
    }>;
  };
}

interface GrowthMetricsSectionProps {
  data: GrowthMetrics;
}

const GrowthMetricsSection = ({ data }: GrowthMetricsSectionProps) => {
  const { cagr } = data;

  return (
    <Card className="card-elegant">
      <CardHeader>
        <CardTitle className="section-header">
          <TrendingUp className="w-5 h-5 text-primary" />
          CAGR (Compound Annual Growth Rate)
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {cagr.percentage && (
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-success/10 text-success border-success/20">
              <TrendingUp className="w-3 h-3 mr-1" />
              {cagr.benchmark}
            </Badge>
          </div>
        )}
        
        {cagr.data_points.length > 0 && (
          <div className="space-y-4">
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={cagr.data_points}>
                  <XAxis 
                    dataKey="year" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                    tickFormatter={(value) => `${value}T`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    stroke="hsl(var(--primary))" 
                    strokeWidth={3}
                    dot={{ fill: 'hsl(var(--primary))', strokeWidth: 2, r: 6 }}
                    activeDot={{ r: 8, fill: 'hsl(var(--primary))' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            
            <div className="grid grid-cols-3 md:grid-cols-6 gap-2 text-center">
              {cagr.data_points.map((point, index) => (
                <div key={index} className="text-xs text-muted-foreground">
                  <div className="font-medium">IDR {point.value}T</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default GrowthMetricsSection;