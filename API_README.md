# Rakamin Profile Viz API

This project now includes a backend API server that provides authentication and company profiling services using the Gemini API.

## Setup Instructions

### 1. Environment Configuration

Update the `.env` file with your actual Gemini API key:

```env
# Server Configuration
PORT=3001

# Login Credentials
LOGIN_EMAIL=<EMAIL>
LOGIN_PASSWORD=Rakamin2025

# Gemini API Configuration
GEMINI_API_KEY=your_actual_gemini_api_key_here
MODEL_ID=gemini-2.5-pro
```

### 2. Getting a Gemini API Key

1. Go to [Google AI Studio](https://aistudio.google.com/)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key and replace `your_actual_gemini_api_key_here` in the `.env` file

### 3. Running the Application

#### Start the Backend Server
```bash
npm run server
```
The server will run on `http://localhost:3001`

#### Start the Frontend (in a separate terminal)
```bash
npm run dev
```
The frontend will run on `http://localhost:8080`

## API Endpoints

### POST /login
Authenticates users with credentials from environment variables.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Rakamin2025"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "token": "dummy-jwt-token"
}
```

### POST /company_profiling
Generates comprehensive company profiles using the Gemini API.

**Request Body:**
```json
{
  "companyName": "PT Rakamin Kolektif Madani"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "company_profile": {
      "basic_info": {
        "company_name": "PT Rakamin Kolektif Madani",
        "logo_url": "...",
        "industry_category": "...",
        // ... more fields
      },
      // ... other sections
    }
  }
}
```

### GET /health
Health check endpoint to verify server status.

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2025-01-19T..."
}
```

## Development Notes

- The backend server uses Express.js with TypeScript
- CORS is enabled for frontend communication
- The Gemini API integration includes web search and URL context tools
- Error handling is implemented for both API failures and parsing errors
- The frontend has been updated to use the actual API endpoints instead of mock data

## Troubleshooting

1. **Server won't start**: Make sure all dependencies are installed with `npm install`
2. **API key errors**: Verify your Gemini API key is correct and has proper permissions
3. **CORS issues**: Ensure the backend server is running on port 3001
4. **Frontend connection issues**: Check that both frontend and backend servers are running
